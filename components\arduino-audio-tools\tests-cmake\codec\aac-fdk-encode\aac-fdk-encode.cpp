// Simple wrapper for Ard<PERSON>o sketch to compilable with cpp in cmake
#include "Arduino.h"
#include "AudioTools.h"
#include "AudioTools/AudioCodecs/CodecAACFDK.h"
//#include <stdlib.h>  // for rand

using namespace audio_tools;  

HexDumpOutput out(Serial);
AACEncoderFDK aac(out);
AudioInfo info;
int16_t buffer[512];

void setup() {
    Serial.begin(115200);

    info.channels = 1;
    info.sample_rate = 16000;
    aac.begin(info);

    Serial.println("starting...");

}

void loop() {
    for (int j=0;j<512;j++){
        buffer[j] = (rand() % 100) - 50;         
    }
    if (aac.write((uint8_t*)buffer, 512*sizeof(int16_t))){
        out.flush();
        Serial.println("512 samples of random data written");
    }
}

