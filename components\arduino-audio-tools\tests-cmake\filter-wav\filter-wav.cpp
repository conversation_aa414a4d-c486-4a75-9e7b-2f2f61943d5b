// Simple wrapper for Arduino sketch to compilable with cpp in cmake
#include "Arduino.h"
#include "AudioTools.h"
#include "AudioTools/AudioLibs/PortAudioStream.h"
#include "SdFat.h"

// define FIR filter https://fiiir.com/
// sampleing rate : 44100
// cutoff frequency: 2000
// Transition bandwidth: 2000
float coef[] = { 
    0.000188725585189891,
    0.000091879473225926,
    -0.000016920442768766,
    -0.000133280461026043,
    -0.000251640080720683,
    -0.000364923626390918,
    -0.000464412185430885,
    -0.000539946399700805,
    -0.000580528007418057,
    -0.000575329172682575,
    -0.000515050625648420,
    -0.000393501287598472,
    -0.000209212939699419,
    0.000033137236602991,
    0.000321738209246393,
    0.000637316639800423,
    0.000953645970946306,
    0.001238992395833948,
    0.001458495336203241,
    0.001577364656923383,
    0.001564660155918535,
    0.001397314635940224,
    0.001063983705740325,
    0.000568265297502738,
    -0.000069161528118792,
    -0.000809871255352040,
    -0.001598785008585991,
    -0.002367155523627244,
    -0.003037394102238113,
    -0.003529490734672840,
    -0.003768583450610565,
    -0.003693062300853045,
    -0.003262467087264144,
    -0.002464373079849631,
    -0.001319467692097694,
    0.000115890783337027,
    0.001750175413602121,
    0.003460562753784068,
    0.005100198277458642,
    0.006508564756602457,
    0.007524348715512882,
    0.007999914326580530,
    0.007816265949382166,
    0.006897235277941903,
    0.005221586005661731,
    0.002831799051373308,
    -0.000161514004361169,
    -0.003580331626899644,
    -0.007185463286171087,
    -0.010688262221784543,
    -0.013767303340739213,
    -0.016089076144421111,
    -0.017331352983485829,
    -0.017207593283301702,
    -0.015490564521582872,
    -0.012033321379788043,
    -0.006785795029102398,
    0.000194498174472729,
    0.008738731003948556,
    0.018570633247558595,
    0.029318005390909025,
    0.040531349219315310,
    0.051708529390585928,
    0.062323892873769583,
    0.071859896969611939,
    0.079839064047870847,
    0.085854013433190587,
    0.089593426008022170,
    0.090862068888852371,
    0.089593426008022170,
    0.085854013433190587,
    0.079839064047870847,
    0.071859896969611939,
    0.062323892873769583,
    0.051708529390585928,
    0.040531349219315317,
    0.029318005390909029,
    0.018570633247558595,
    0.008738731003948556,
    0.000194498174472729,
    -0.006785795029102398,
    -0.012033321379788046,
    -0.015490564521582872,
    -0.017207593283301702,
    -0.017331352983485829,
    -0.016089076144421115,
    -0.013767303340739216,
    -0.010688262221784546,
    -0.007185463286171090,
    -0.003580331626899644,
    -0.000161514004361169,
    0.002831799051373308,
    0.005221586005661734,
    0.006897235277941905,
    0.007816265949382166,
    0.007999914326580530,
    0.007524348715512883,
    0.006508564756602457,
    0.005100198277458645,
    0.003460562753784068,
    0.001750175413602121,
    0.000115890783337027,
    -0.001319467692097694,
    -0.002464373079849632,
    -0.003262467087264142,
    -0.003693062300853045,
    -0.003768583450610565,
    -0.003529490734672841,
    -0.003037394102238113,
    -0.002367155523627246,
    -0.001598785008585990,
    -0.000809871255352040,
    -0.000069161528118792,
    0.000568265297502738,
    0.001063983705740327,
    0.001397314635940224,
    0.001564660155918536,
    0.001577364656923383,
    0.001458495336203241,
    0.001238992395833948,
    0.000953645970946307,
    0.000637316639800423,
    0.000321738209246393,
    0.000033137236602991,
    -0.000209212939699420,
    -0.000393501287598472,
    -0.000515050625648420,
    -0.000575329172682575,
    -0.000580528007418057,
    -0.000539946399700805,
    -0.000464412185430885,
    -0.000364923626390918,
    -0.000251640080720683,
    -0.000133280461026043,
    -0.000016920442768766,
    0.000091879473225926,
    0.000188725585189891
};
// 
uint16_t sample_rate=44100;
uint8_t channels = 2;                                             // The stream will have 2 channels 
SineWaveGenerator<int16_t> wave(32000);                             // subclass of SoundGenerator with max amplitude of 32000
GeneratedSoundStream<int16_t> in_stream(wave);                   // Stream generated from sine wave
FilteredStream<int16_t, float> in_filtered(in_stream, channels);  // Defiles the filter as BaseConverter
SdFat sd;
SdFile file;
EncodedAudioStream out(&file, new WAVEncoder());                  // encode as wav file
StreamCopy copier(out, in_filtered);                                // copies sound to out
MusicalNotes notes;

void setup(){
  Serial.begin(115200);
  AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Info);  

  auto cfg = wave.defaultConfig();
  cfg.sample_rate = sample_rate;
  cfg.channels = channels;
  cfg.bits_per_sample = 16;
  wave.begin(cfg, 20);
  in_stream.begin();
  out.begin();

  // setup filters on channel 1 only
  in_filtered.setFilter(1, new FIR<float>(coef));
  
  if (!sd.begin(SS, SPI_HALF_SPEED)) sd.initErrorHalt();
  if (!file.open("wave.wav", O_RDWR | O_CREAT)) {
    sd.errorHalt("opening wave.wav for write failed");
  }

  for (int j=45;j<notes.frequencyCount();j++){
      wave.setFrequency(notes.frequency(j));
      for (int i=0;i<10;i++){
        copier.copy();
      }
  }
  file.close();
  stop();
}

void loop(){
}

