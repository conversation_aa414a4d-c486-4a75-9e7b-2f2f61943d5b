#pragma once
/**
 * @defgroup collections Collections
 * @ingroup tools
 * @brief Vector, List, Queue, Stack...
 */

#include "AudioTools/CoreAudio/AudioBasic/Collections/Vector.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/List.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/Stack.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/Queue.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/QueueFromVector.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/BitVector.h"
#include "AudioTools/CoreAudio/AudioBasic/Collections/Slice.h"