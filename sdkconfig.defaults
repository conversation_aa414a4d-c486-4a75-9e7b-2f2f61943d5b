# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF)  Project Minimal Configuration
#
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_COMPILER_OPTIMIZATION_PERF=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=y
CONFIG_BT_ENABLED=y
CONFIG_BT_BTC_TASK_STACK_SIZE=16384
CONFIG_BT_BTU_TASK_STACK_SIZE=8192
CONFIG_BT_CLASSIC_ENABLED=y
CONFIG_BT_A2DP_ENABLE=y
CONFIG_BT_A2DP_APTX_DECODER=y
CONFIG_BT_A2DP_LDAC_DECODER=y
CONFIG_BT_A2DP_OPUS_DECODER=y
CONFIG_BT_A2DP_LC3PLUS_DECODER=y
CONFIG_BT_A2DP_AAC_DECODER=y
CONFIG_BT_BLE_ENABLED=n
CONFIG_BTDM_CTRL_MODE_BR_EDR_ONLY=y
CONFIG_TWAI_ERRATA_FIX_BUS_OFF_REC=n
CONFIG_TWAI_ERRATA_FIX_TX_INTR_LOST=n
CONFIG_TWAI_ERRATA_FIX_RX_FRAME_INVALID=n
CONFIG_TWAI_ERRATA_FIX_RX_FIFO_CORRUPT=n
CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH=y
CONFIG_ESP32_REV_MIN_1=y
CONFIG_ESP_PHY_REDUCE_TX_POWER=y
CONFIG_PM_ENABLE=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_USE_CAPS_ALLOC=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_ESP_INT_WDT_TIMEOUT_MS=300
CONFIG_ESP_IPC_TASK_STACK_SIZE=1536
CONFIG_ESP_WIFI_STATIC_TX_BUFFER=y
CONFIG_ESP_WIFI_IRAM_OPT=y
CONFIG_ESP_WIFI_RX_IRAM_OPT=y
CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY=y
CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH=y
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=5744
CONFIG_LWIP_TCP_WND_DEFAULT=5744
CONFIG_MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER=y
CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2=n
CONFIG_WIFI_PROV_BLE_FORCE_ENCRYPTION=y
