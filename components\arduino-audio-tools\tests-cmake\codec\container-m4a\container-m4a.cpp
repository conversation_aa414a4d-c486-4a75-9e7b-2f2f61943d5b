/**
 * @file communication-container-binary.ino
 * <AUTHOR>
 * @brief generate sine wave -> encoder -> decoder -> audiokit (i2s)
 * @version 0.1
 * @date 2022-04-30
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#include "AudioTools.h"
#include "AudioTools/AudioCodecs/ContainerMP4.h"
//#include "AudioTools/AudioLibs/StdioStream.h"
//const char *file = "/home/<USER>/Development/Mp4Parser/sample-1.m4a";
const char* file_str = "/home/<USER>/Downloads/test.m4a";
auto file = SD.open(file_str, FILE_READ);
CsvOutput<int16_t> out(Serial);
ContainerMP4 mp4;
EncodedAudioStream codec(&out, &mp4);
StreamCopy copier(codec, file);     

void setup() {
  AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Info);

  // start 
  Serial.println("starting...");
  auto cfgi = out.defaultConfig(TX_MODE);
  out.begin(cfgi);

  codec.begin();

  Serial.println("Test started...");
}


void loop() { 
  if (!copier.copy()){
    exit(0);
  }
}