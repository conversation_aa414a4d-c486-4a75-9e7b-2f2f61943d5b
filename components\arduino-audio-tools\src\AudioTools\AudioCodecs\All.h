#pragma once

// All codecs, so that we can find any compile errors easily
// This only works, when you have all codecs installed!
#include "AudioTools/AudioCodecs/AudioEncoded.h"
#include "AudioTools/AudioCodecs/ContainerOgg.h"
#include "AudioTools/AudioCodecs/CodecOpus.h"
#include "AudioTools/AudioCodecs/CodecOpusOgg.h"
#include "AudioTools/AudioCodecs/CodecFLAC.h"
#include "AudioTools/AudioCodecs/CodecVorbis.h"
#include "AudioTools/AudioCodecs/CodecADPCM.h"
//#include "AudioTools/AudioCodecs/CodecCodec2.h"
#include "AudioTools/AudioCodecs/CodecGSM.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"
#include "AudioTools/AudioCodecs/CodecAACHelix.h"
#include "AudioTools/AudioCodecs/ContainerBinary.h"
#include "AudioTools/AudioCodecs/CodecADPCMXQ.h"
#include "AudioTools/AudioCodecs/CodecCopy.h"
#include "AudioTools/AudioCodecs/CodecHelix.h"
#include "AudioTools/AudioCodecs/CodecMP3LAME.h"
#include "AudioTools/AudioCodecs/CodecSBC.h"
#include "AudioTools/AudioCodecs/ContainerMP4.h"
#include "AudioTools/AudioCodecs/AudioFormat.h"
#include "AudioTools/AudioCodecs/CodecADTS.h"
#include "AudioTools/AudioCodecs/CodecILBC.h"
#include "AudioTools/AudioCodecs/CodecMP3MAD.h"
#include "AudioTools/AudioCodecs/CodecAACFAAD.h"
#include "AudioTools/AudioCodecs/CodecAPTX.h"
#include "AudioTools/AudioCodecs/CodecFloat.h"
#include "AudioTools/AudioCodecs/CodecL16.h"
#include "AudioTools/AudioCodecs/CodecWAV.h"
#include "AudioTools/AudioCodecs/CodecAACFDK.h"
#include "AudioTools/AudioCodecs/CodecBase64.h"
#include "AudioTools/AudioCodecs/CodecG722.h"
#include "AudioTools/AudioCodecs/CodecL8.h"
#include "AudioTools/AudioCodecs/CodecMTS.h"
#include "AudioTools/AudioCodecs/CodecWavIMA.h"
#include "AudioTools/AudioCodecs/CodecBasic.h"
#include "AudioTools/AudioCodecs/CodecG7xx.h"
#include "AudioTools/AudioCodecs/CodecLC3.h"
#include "AudioTools/AudioCodecs/ContainerAVI.h"
#include "AudioTools/AudioCodecs/DecoderFromStreaming.h"
//#include "AudioTools/AudioCodecs/CodecMP3Mini.h"
